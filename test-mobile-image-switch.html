<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>移动端图片切换测试</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        /* 模拟移动端环境 */
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        
        /* 确保移动端图片容器正确显示 */
        .computer-img-container-mb.mb-only {
            display: block !important;
            min-height: 200px;
            text-align: center;
            padding: 10px;
            border: 2px solid #ddd;
            margin: 20px 0;
        }

        .computer-img-container-mb.mb-only img {
            display: none !important;
            width: 90% !important;
            height: auto !important;
            margin: 0 auto !important;
            max-width: 300px;
        }

        /* 显示激活的图片 */
        .computer-img-container-mb.mb-only img.active-mobile-img {
            display: block !important;
        }
        
        /* 按钮样式 */
        .btn-container {
            text-align: center;
            margin: 20px 0;
        }
        
        .btn {
            display: inline-block;
            padding: 10px 20px;
            margin: 5px;
            background: #f0f0f0;
            border: 1px solid #ddd;
            cursor: pointer;
            border-radius: 4px;
        }
        
        .btn.active {
            background: #4F7FE8;
            color: white;
            border-color: #4F7FE8;
        }
        
        .btn:hover {
            background: #e0e0e0;
        }
        
        .btn.active:hover {
            background: #3a6bc7;
        }
    </style>
</head>
<body>
    <h1>移动端图片切换测试</h1>
    
    <div class="btn-container">
        <div class="btn active">账号密码登录</div>
        <div class="btn">移动端扫码登录</div>
        <div class="btn">修改密码</div>
    </div>
    
    <div class="computer-img-container-mb mb-only">
        <img src="https://via.placeholder.com/300x200/ff6b6b/ffffff?text=账号密码登录" alt="账号密码登录" />
        <img src="https://via.placeholder.com/300x200/4ecdc4/ffffff?text=移动端扫码登录" alt="移动端扫码登录" />
        <img src="https://via.placeholder.com/300x200/45b7d1/ffffff?text=修改密码" alt="修改密码" />
    </div>
    
    <div id="debug-info">
        <h3>调试信息：</h3>
        <div id="debug-output"></div>
    </div>

    <script>
        $(function() {
            // 初始化移动端图片显示状态
            $('.computer-img-container-mb').children('img').removeClass('active-mobile-img');
            $('.computer-img-container-mb').children('img').eq(0).addClass('active-mobile-img');
            
            //初始化，默认第一个选中
            showComputerPane(0);

            // 调试：检查按钮数量
            console.log('按钮容器数量:', $('.btn-container').length);
            console.log('按钮数量:', $('.btn-container').children('div').length);

            $('.btn-container').children('div').each(function(i) {
                console.log('绑定按钮', i, ':', $(this).text());
                $(this).click(function() {
                    console.log('点击了按钮', i, ':', $(this).text());
                    showComputerPane(i);
                    //去掉所有active状态，再给当前选中的赋予active
                    $(".btn-container div").removeClass("active");
                    $(this).addClass("active");
                });
            });
        });

        function showComputerPane(index) {
            // 移动端图片切换 - 使用类名切换方式
            $('.computer-img-container-mb').children('img').removeClass('active-mobile-img');
            $('.computer-img-container-mb').children('img').eq(index).addClass('active-mobile-img');

            // 调试信息
            console.log('切换到图片索引:', index);
            console.log('移动端容器数量:', $('.computer-img-container-mb').length);
            console.log('移动端图片数量:', $('.computer-img-container-mb').children('img').length);
            console.log('当前激活的图片:', $('.computer-img-container-mb').children('img').eq(index).attr('src'));
            
            // 更新调试信息显示
            $('#debug-output').html(`
                <p>当前索引: ${index}</p>
                <p>容器数量: ${$('.computer-img-container-mb').length}</p>
                <p>图片数量: ${$('.computer-img-container-mb').children('img').length}</p>
                <p>激活的图片类名: ${$('.computer-img-container-mb').children('img').eq(index).hasClass('active-mobile-img') ? '已添加' : '未添加'}</p>
            `);
        }
    </script>
</body>
</html>
